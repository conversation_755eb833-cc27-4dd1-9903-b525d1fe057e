{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/src/components/AnimatedBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface Particle {\n  id: number;\n  x: number;\n  y: number;\n  size: number;\n  speedX: number;\n  speedY: number;\n  opacity: number;\n}\n\nexport default function AnimatedBackground() {\n  const [particles, setParticles] = useState<Particle[]>([]);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    // 创建粒子\n    const createParticles = () => {\n      const newParticles: Particle[] = [];\n      for (let i = 0; i < 100; i++) {\n        newParticles.push({\n          id: i,\n          x: Math.random() * window.innerWidth,\n          y: Math.random() * window.innerHeight,\n          size: Math.random() * 3 + 1,\n          speedX: (Math.random() - 0.5) * 0.5,\n          speedY: (Math.random() - 0.5) * 0.5,\n          opacity: Math.random() * 0.5 + 0.1,\n        });\n      }\n      setParticles(newParticles);\n    };\n\n    createParticles();\n\n    // 动画循环\n    const animateParticles = () => {\n      setParticles(prevParticles => \n        prevParticles.map(particle => ({\n          ...particle,\n          x: particle.x + particle.speedX,\n          y: particle.y + particle.speedY,\n          x: particle.x > window.innerWidth ? 0 : particle.x < 0 ? window.innerWidth : particle.x,\n          y: particle.y > window.innerHeight ? 0 : particle.y < 0 ? window.innerHeight : particle.y,\n        }))\n      );\n    };\n\n    const interval = setInterval(animateParticles, 50);\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      createParticles();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      clearInterval(interval);\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  if (!mounted) return null;\n\n  return (\n    <div className=\"fixed inset-0 pointer-events-none z-0\">\n      {/* 渐变背景 */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\" />\n      \n      {/* 动态网格 */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div \n          className=\"w-full h-full\"\n          style={{\n            backgroundImage: `\n              linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),\n              linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px)\n            `,\n            backgroundSize: '50px 50px',\n            animation: 'float 20s ease-in-out infinite',\n          }}\n        />\n      </div>\n\n      {/* 粒子系统 */}\n      {particles.map(particle => (\n        <div\n          key={particle.id}\n          className=\"absolute rounded-full bg-white\"\n          style={{\n            left: `${particle.x}px`,\n            top: `${particle.y}px`,\n            width: `${particle.size}px`,\n            height: `${particle.size}px`,\n            opacity: particle.opacity,\n            boxShadow: `0 0 ${particle.size * 2}px rgba(139, 92, 246, 0.3)`,\n          }}\n        />\n      ))}\n\n      {/* 光晕效果 */}\n      <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse-glow\" />\n      <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse-glow\" style={{ animationDelay: '1s' }} />\n      <div className=\"absolute top-3/4 left-1/2 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse-glow\" style={{ animationDelay: '2s' }} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,OAAO;QACP,MAAM,kBAAkB;YACtB,MAAM,eAA2B,EAAE;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,aAAa,IAAI,CAAC;oBAChB,IAAI;oBACJ,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;oBACpC,GAAG,KAAK,MAAM,KAAK,OAAO,WAAW;oBACrC,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAChC,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAChC,SAAS,KAAK,MAAM,KAAK,MAAM;gBACjC;YACF;YACA,aAAa;QACf;QAEA;QAEA,OAAO;QACP,MAAM,mBAAmB;YACvB,aAAa,CAAA,gBACX,cAAc,GAAG,CAAC,CAAA,WAAY,CAAC;wBAC7B,GAAG,QAAQ;wBACX,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM;wBAC/B,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM;wBAC/B,GAAG,SAAS,CAAC,GAAG,OAAO,UAAU,GAAG,IAAI,SAAS,CAAC,GAAG,IAAI,OAAO,UAAU,GAAG,SAAS,CAAC;wBACvF,GAAG,SAAS,CAAC,GAAG,OAAO,WAAW,GAAG,IAAI,SAAS,CAAC,GAAG,IAAI,OAAO,WAAW,GAAG,SAAS,CAAC;oBAC3F,CAAC;QAEL;QAEA,MAAM,WAAW,YAAY,kBAAkB;QAE/C,WAAW;QACX,MAAM,eAAe;YACnB;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,cAAc;YACd,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC;;;YAGlB,CAAC;wBACD,gBAAgB;wBAChB,WAAW;oBACb;;;;;;;;;;;YAKH,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC;oBAEC,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;wBACvB,KAAK,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;wBACtB,OAAO,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC;wBAC3B,QAAQ,GAAG,SAAS,IAAI,CAAC,EAAE,CAAC;wBAC5B,SAAS,SAAS,OAAO;wBACzB,WAAW,CAAC,IAAI,EAAE,SAAS,IAAI,GAAG,EAAE,0BAA0B,CAAC;oBACjE;mBATK,SAAS,EAAE;;;;;0BAcpB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;gBAAkG,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;0BAC/I,8OAAC;gBAAI,WAAU;gBAA8F,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;;;;;;;AAGjJ", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/src/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface LoadingScreenProps {\n  onComplete: () => void;\n}\n\nexport default function LoadingScreen({ onComplete }: LoadingScreenProps) {\n  const [progress, setProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(true);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(timer);\n          setTimeout(() => {\n            setIsVisible(false);\n            onComplete();\n          }, 500);\n          return 100;\n        }\n        return prev + Math.random() * 15 + 5;\n      });\n    }, 200);\n\n    return () => clearInterval(timer);\n  }, [onComplete]);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-100 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center\">\n      {/* 背景动画 */}\n      <div className=\"absolute inset-0\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white/20 rounded-full animate-ping\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 2}s`,\n              animationDuration: `${2 + Math.random() * 2}s`,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 text-center\">\n        {/* Logo动画 */}\n        <div className=\"mb-12\">\n          <h1 className=\"text-6xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-gradient-x\">\n            PREMIUM\n          </h1>\n          <div className=\"mt-4 flex justify-center space-x-1\">\n            {[...Array(3)].map((_, i) => (\n              <div\n                key={i}\n                className=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce\"\n                style={{ animationDelay: `${i * 0.2}s` }}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* 进度条 */}\n        <div className=\"w-80 mx-auto\">\n          <div className=\"flex justify-between text-sm text-white/60 mb-2\">\n            <span>加载中...</span>\n            <span>{Math.round(progress)}%</span>\n          </div>\n          \n          <div className=\"w-full h-1 bg-white/10 rounded-full overflow-hidden\">\n            <div \n              className=\"h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-300 ease-out relative\"\n              style={{ width: `${progress}%` }}\n            >\n              <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer\" />\n            </div>\n          </div>\n        </div>\n\n        {/* 加载文本 */}\n        <div className=\"mt-8 text-white/70\">\n          <p className=\"text-lg\">正在为您准备极致体验</p>\n          <div className=\"mt-2 flex justify-center items-center space-x-2\">\n            <div className=\"w-1 h-1 bg-purple-400 rounded-full animate-pulse\" />\n            <div className=\"w-1 h-1 bg-pink-400 rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }} />\n            <div className=\"w-1 h-1 bg-blue-400 rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }} />\n          </div>\n        </div>\n\n        {/* 旋转环 */}\n        <div className=\"absolute -top-20 -right-20 w-40 h-40 border border-purple-500/20 rounded-full animate-spin-slow\" />\n        <div className=\"absolute -bottom-20 -left-20 w-32 h-32 border border-pink-500/20 rounded-full animate-spin-slow\" style={{ animationDirection: 'reverse' }} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQe,SAAS,cAAc,EAAE,UAAU,EAAsB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,YAAY,CAAA;gBACV,IAAI,QAAQ,KAAK;oBACf,cAAc;oBACd,WAAW;wBACT,aAAa;wBACb;oBACF,GAAG;oBACH,OAAO;gBACT;gBACA,OAAO,OAAO,KAAK,MAAM,KAAK,KAAK;YACrC;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAW;IAEf,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wBAChD;uBAPK;;;;;;;;;;0BAYX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkI;;;;;;0CAGhJ,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAEC,WAAU;wCACV,OAAO;4CAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;wCAAC;uCAFlC;;;;;;;;;;;;;;;;kCASb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAM,KAAK,KAAK,CAAC;4CAAU;;;;;;;;;;;;;0CAG9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;8CAE/B,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAU;;;;;;0CACvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;wCAAiD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAChG,8OAAC;wCAAI,WAAU;wCAAiD,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;kCAKpG,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAAkG,OAAO;4BAAE,oBAAoB;wBAAU;;;;;;;;;;;;;;;;;;AAIhK", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport AnimatedBackground from '@/components/AnimatedBackground';\nimport LoadingScreen from '@/components/LoadingScreen';\n\nexport default function Home() {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [showLoading, setShowLoading] = useState(true);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const handleLoadingComplete = () => {\n    setShowLoading(false);\n    setTimeout(() => setIsLoaded(true), 100);\n  };\n\n  return (\n    <>\n      {showLoading && <LoadingScreen onComplete={handleLoadingComplete} />}\n\n      <div className=\"min-h-screen overflow-hidden relative\">\n        <AnimatedBackground />\n\n      {/* 鼠标跟随光效 */}\n      <div\n        className=\"fixed w-96 h-96 bg-gradient-radial from-purple-500/20 via-blue-500/10 to-transparent rounded-full pointer-events-none blur-xl transition-all duration-300 ease-out\"\n        style={{\n          left: mousePosition.x - 192,\n          top: mousePosition.y - 192,\n        }}\n      />\n\n      {/* 导航栏 */}\n      <nav className={`fixed top-0 w-full z-50 transition-all duration-1000 ${isLoaded ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}`}>\n        <div className=\"backdrop-blur-md bg-white/5 border-b border-white/10\">\n          <div className=\"max-w-7xl mx-auto px-6 py-4\">\n            <div className=\"flex justify-between items-center\">\n              <div className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                PREMIUM\n              </div>\n              <div className=\"hidden md:flex space-x-8\">\n                {['首页', '关于', '服务', '作品', '联系'].map((item, index) => (\n                  <a\n                    key={item}\n                    href=\"#\"\n                    className={`text-white/80 hover:text-white transition-all duration-300 hover:scale-105 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}\n                    style={{ transitionDelay: `${index * 100}ms` }}\n                  >\n                    {item}\n                  </a>\n                ))}\n              </div>\n              <button className=\"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-purple-500/25\">\n                开始体验\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* 主要内容 */}\n      <main className=\"relative z-10 pt-24\">\n        {/* 英雄区域 */}\n        <section className=\"min-h-screen flex items-center justify-center px-6\">\n          <div className=\"max-w-6xl mx-auto text-center\">\n            <div className={`transition-all duration-1500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>\n              <h1 className=\"text-6xl md:text-8xl font-bold mb-8 leading-tight\">\n                <span className=\"bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-gradient-x\">\n                  创造无限\n                </span>\n                <br />\n                <span className=\"text-white\">可能性</span>\n              </h1>\n\n              <p className={`text-xl md:text-2xl text-white/70 mb-12 max-w-3xl mx-auto leading-relaxed transition-all duration-1500 delay-300 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>\n                体验前所未有的数字艺术之旅，在这里，每一个像素都承载着无限的创意与激情\n              </p>\n\n              <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center transition-all duration-1500 delay-500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>\n                <button className=\"group relative px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full font-semibold text-lg overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25\">\n                  <span className=\"relative z-10\">探索奇迹</span>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                </button>\n\n                <button className=\"group px-8 py-4 border-2 border-white/20 text-white rounded-full font-semibold text-lg backdrop-blur-sm hover:border-white/40 transition-all duration-300 hover:scale-105 hover:bg-white/5\">\n                  了解更多\n                  <span className=\"inline-block ml-2 transition-transform duration-300 group-hover:translate-x-1\">→</span>\n                </button>\n              </div>\n            </div>\n\n            {/* 浮动元素 */}\n            <div className=\"absolute inset-0 pointer-events-none\">\n              {[...Array(6)].map((_, i) => (\n                <div\n                  key={i}\n                  className={`absolute w-20 h-20 border border-white/10 rounded-full transition-all duration-2000 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}\n                  style={{\n                    left: `${10 + (i * 15)}%`,\n                    top: `${20 + (i % 3) * 20}%`,\n                    animationDelay: `${i * 200}ms`,\n                    animation: `float ${3 + (i % 3)}s ease-in-out infinite`,\n                  }}\n                />\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* 特色区域 */}\n        <section className=\"py-24 px-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-6xl font-bold text-white mb-6\">\n                精心雕琢的\n                <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\"> 艺术品</span>\n              </h2>\n              <p className=\"text-xl text-white/70 max-w-2xl mx-auto\">\n                每一个细节都经过精心设计，为您带来无与伦比的视觉体验\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              {[\n                { title: '极致设计', desc: '追求完美的视觉呈现', icon: '✨' },\n                { title: '流畅动画', desc: '丝滑般的交互体验', icon: '🎭' },\n                { title: '响应式布局', desc: '完美适配所有设备', icon: '📱' },\n              ].map((item, index) => (\n                <div\n                  key={item.title}\n                  className={`group p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-500 hover:scale-105 hover:bg-white/10 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}\n                  style={{ transitionDelay: `${index * 200}ms` }}\n                >\n                  <div className=\"text-4xl mb-4 group-hover:scale-110 transition-transform duration-300\">\n                    {item.icon}\n                  </div>\n                  <h3 className=\"text-2xl font-bold text-white mb-4\">{item.title}</h3>\n                  <p className=\"text-white/70 leading-relaxed\">{item.desc}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* 页脚 */}\n      <footer className=\"relative z-10 py-12 px-6 border-t border-white/10\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <div className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4\">\n            PREMIUM\n          </div>\n          <p className=\"text-white/50\">\n            © 2024 Premium Design. 用心创造每一个像素的完美。\n          </p>\n        </div>\n      </footer>\n\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-20px) rotate(180deg); }\n        }\n\n        @keyframes gradient-x {\n          0%, 100% { background-size: 200% 200%; background-position: left center; }\n          50% { background-size: 200% 200%; background-position: right center; }\n        }\n\n        .animate-gradient-x {\n          animation: gradient-x 3s ease infinite;\n        }\n      `}</style>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,eAAe;QACf,WAAW,IAAM,YAAY,OAAO;IACtC;IAEA,qBACE;;YACG,6BAAe,8OAAC,mIAAA,CAAA,UAAa;gBAAC,YAAY;;;;;;0BAE3C,8OAAC;0DAAc;;kCACb,8OAAC,wIAAA,CAAA,UAAkB;;;;;kCAGrB,8OAAC;wBAEC,OAAO;4BACL,MAAM,cAAc,CAAC,GAAG;4BACxB,KAAK,cAAc,CAAC,GAAG;wBACzB;kEAJU;;;;;;kCAQZ,8OAAC;kEAAe,CAAC,qDAAqD,EAAE,WAAW,8BAA8B,+BAA+B;kCAC9I,cAAA,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDAAgG;;;;;;sDAG/G,8OAAC;sFAAc;sDACZ;gDAAC;gDAAM;gDAAM;gDAAM;gDAAM;6CAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzC,8OAAC;oDAEC,MAAK;oDAEL,OAAO;wDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oDAAC;8FADlC,CAAC,2EAA2E,EAAE,WAAW,8BAA8B,2BAA2B;8DAG5J;mDALI;;;;;;;;;;sDASX,8OAAC;sFAAiB;sDAAkK;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5L,8OAAC;kEAAe;;0CAEd,8OAAC;0EAAkB;0CACjB,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAe,CAAC,6BAA6B,EAAE,WAAW,8BAA8B,4BAA4B;;8DACnH,8OAAC;8FAAa;;sEACZ,8OAAC;sGAAe;sEAA+G;;;;;;sEAG/H,8OAAC;;;;;;;sEACD,8OAAC;sGAAe;sEAAa;;;;;;;;;;;;8DAG/B,8OAAC;8FAAa,CAAC,iHAAiH,EAAE,WAAW,8BAA8B,4BAA4B;8DAAE;;;;;;8DAIzM,8OAAC;8FAAe,CAAC,mGAAmG,EAAE,WAAW,8BAA8B,4BAA4B;;sEACzL,8OAAC;sGAAiB;;8EAChB,8OAAC;8GAAe;8EAAgB;;;;;;8EAChC,8OAAC;8GAAc;;;;;;;;;;;;sEAGjB,8OAAC;sGAAiB;;gEAA6L;8EAE7M,8OAAC;8GAAe;8EAAgF;;;;;;;;;;;;;;;;;;;;;;;;sDAMtG,8OAAC;sFAAc;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oDAGC,OAAO;wDACL,MAAM,GAAG,KAAM,IAAI,GAAI,CAAC,CAAC;wDACzB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;wDAC5B,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;wDAC9B,WAAW,CAAC,MAAM,EAAE,IAAK,IAAI,EAAG,sBAAsB,CAAC;oDACzD;8FANW,CAAC,oFAAoF,EAAE,WAAW,0BAA0B,qBAAqB;mDADvJ;;;;;;;;;;;;;;;;;;;;;0CAef,8OAAC;0EAAkB;0CACjB,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAa;;wDAAiD;sEAE7D,8OAAC;sGAAe;sEAA6E;;;;;;;;;;;;8DAE/F,8OAAC;8FAAY;8DAA0C;;;;;;;;;;;;sDAKzD,8OAAC;sFAAc;sDACZ;gDACC;oDAAE,OAAO;oDAAQ,MAAM;oDAAa,MAAM;gDAAI;gDAC9C;oDAAE,OAAO;oDAAQ,MAAM;oDAAY,MAAM;gDAAK;gDAC9C;oDAAE,OAAO;oDAAS,MAAM;oDAAY,MAAM;gDAAK;6CAChD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;oDAGC,OAAO;wDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oDAAC;8FADlC,CAAC,6JAA6J,EAAE,WAAW,8BAA8B,4BAA4B;;sEAGhP,8OAAC;sGAAc;sEACZ,KAAK,IAAI;;;;;;sEAEZ,8OAAC;sGAAa;sEAAsC,KAAK,KAAK;;;;;;sEAC9D,8OAAC;sGAAY;sEAAiC,KAAK,IAAI;;;;;;;mDARlD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiB3B,8OAAC;kEAAiB;kCAChB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CAAqG;;;;;;8CAGpH,8OAAC;8EAAY;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBvC", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": ";AACA,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,YAAY,eAAe,QAAQ,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,gBAAkB,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,sCAAO,0BAA+B;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,gBAAkB,eAAe,IAAI,CAAC,iBAAiB;;QAY3D,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,wCAAmC;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;;;QAEI,IAAI;QAeJ,IAAI;IAIZ;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,gBAAkB,aAAa;YACzD,IAAI,QAAQ,sCAAgC,0BAAkB,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ;;YACI,IAAI;;QAIR,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,wCAAmC;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;;;QAII,IAAI;IAKZ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB;;aAKO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,wCAAmC;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;;;IAWJ;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,wCAAmC;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,gBAAkB,eAAe,CAAC,IAAI,CAAC,WAAW;;QAOtD,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ,CAAC;QACrB,OAAO,gBAAgB,sBAAsB;IACjD,IAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,sCAAgC,0BAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,wCAAmC;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;;;AAYJ;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/premium-website/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,wGAAwB,KAAK", "ignoreList": [0], "debugId": null}}]}
'use client';

import { useState, useEffect } from 'react';
import AnimatedBackground from '@/components/AnimatedBackground';
import LoadingScreen from '@/components/LoadingScreen';

export default function Home() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);
  const [showLoading, setShowLoading] = useState(true);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleLoadingComplete = () => {
    setShowLoading(false);
    setTimeout(() => setIsLoaded(true), 100);
  };

  return (
    <>
      {showLoading && <LoadingScreen onComplete={handleLoadingComplete} />}

      <div className="min-h-screen overflow-hidden relative">
        <AnimatedBackground />

      {/* 鼠标跟随光效 */}
      <div
        className="fixed w-96 h-96 bg-gradient-radial from-purple-500/20 via-blue-500/10 to-transparent rounded-full pointer-events-none blur-xl transition-all duration-300 ease-out"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      />

      {/* 导航栏 */}
      <nav className={`fixed top-0 w-full z-50 transition-all duration-1000 ${isLoaded ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}`}>
        <div className="backdrop-blur-md bg-white/5 border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex justify-between items-center">
              <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                PREMIUM
              </div>
              <div className="hidden md:flex space-x-8">
                {['首页', '关于', '服务', '作品', '联系'].map((item, index) => (
                  <a
                    key={item}
                    href="#"
                    className={`text-white/80 hover:text-white transition-all duration-300 hover:scale-105 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}
                    style={{ transitionDelay: `${index * 100}ms` }}
                  >
                    {item}
                  </a>
                ))}
              </div>
              <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-purple-500/25">
                开始体验
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="relative z-10 pt-24">
        {/* 英雄区域 */}
        <section className="min-h-screen flex items-center justify-center px-6">
          <div className="max-w-6xl mx-auto text-center">
            <div className={`transition-all duration-1500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>
              <h1 className="text-6xl md:text-8xl font-bold mb-8 leading-tight">
                <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-transparent animate-gradient-x">
                  创造无限
                </span>
                <br />
                <span className="text-white">可能性</span>
              </h1>

              <p className={`text-xl md:text-2xl text-white/70 mb-12 max-w-3xl mx-auto leading-relaxed transition-all duration-1500 delay-300 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>
                体验前所未有的数字艺术之旅，在这里，每一个像素都承载着无限的创意与激情
              </p>

              <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center transition-all duration-1500 delay-500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}>
                <button className="group relative px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full font-semibold text-lg overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25">
                  <span className="relative z-10">探索奇迹</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </button>

                <button className="group px-8 py-4 border-2 border-white/20 text-white rounded-full font-semibold text-lg backdrop-blur-sm hover:border-white/40 transition-all duration-300 hover:scale-105 hover:bg-white/5">
                  了解更多
                  <span className="inline-block ml-2 transition-transform duration-300 group-hover:translate-x-1">→</span>
                </button>
              </div>
            </div>

            {/* 浮动元素 */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className={`absolute w-20 h-20 border border-white/10 rounded-full transition-all duration-2000 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-0'}`}
                  style={{
                    left: `${10 + (i * 15)}%`,
                    top: `${20 + (i % 3) * 20}%`,
                    animationDelay: `${i * 200}ms`,
                    animation: `float ${3 + (i % 3)}s ease-in-out infinite`,
                  }}
                />
              ))}
            </div>
          </div>
        </section>

        {/* 特色区域 */}
        <section className="py-24 px-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
                精心雕琢的
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> 艺术品</span>
              </h2>
              <p className="text-xl text-white/70 max-w-2xl mx-auto">
                每一个细节都经过精心设计，为您带来无与伦比的视觉体验
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                { title: '极致设计', desc: '追求完美的视觉呈现', icon: '✨' },
                { title: '流畅动画', desc: '丝滑般的交互体验', icon: '🎭' },
                { title: '响应式布局', desc: '完美适配所有设备', icon: '📱' },
              ].map((item, index) => (
                <div
                  key={item.title}
                  className={`group p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-500 hover:scale-105 hover:bg-white/10 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-12 opacity-0'}`}
                  style={{ transitionDelay: `${index * 200}ms` }}
                >
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    {item.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">{item.title}</h3>
                  <p className="text-white/70 leading-relaxed">{item.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>

      {/* 页脚 */}
      <footer className="relative z-10 py-12 px-6 border-t border-white/10">
        <div className="max-w-7xl mx-auto text-center">
          <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4">
            PREMIUM
          </div>
          <p className="text-white/50">
            © 2024 Premium Design. 用心创造每一个像素的完美。
          </p>
        </div>
      </footer>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes gradient-x {
          0%, 100% { background-size: 200% 200%; background-position: left center; }
          50% { background-size: 200% 200%; background-position: right center; }
        }

        .animate-gradient-x {
          animation: gradient-x 3s ease infinite;
        }
      `}</style>
      </div>
    </>
  );
}
